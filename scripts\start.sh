#!/bin/bash

# Docker负载均衡器启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.21或更高版本"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18或更高版本"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装，请先安装npm"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建后端
build_backend() {
    log_info "构建Go后端..."
    
    # 下载依赖
    go mod tidy
    
    # 构建
    go build -o bin/docker-lb cmd/main.go
    
    log_success "后端构建完成"
}

# 构建前端
build_frontend() {
    log_info "构建Vue前端..."
    
    cd web
    
    # 安装依赖
    npm install
    
    # 构建
    npm run build
    
    cd ..
    
    log_success "前端构建完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p bin
    mkdir -p logs
    mkdir -p ssl
    
    log_success "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "config.yaml" ]; then
        log_warning "config.yaml不存在，使用默认配置"
        cp config.yaml.example config.yaml 2>/dev/null || true
    fi
    
    log_success "配置文件检查完成"
}

# 启动服务
start_service() {
    log_info "启动Docker负载均衡器..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口8080已被占用，尝试停止现有服务..."
        pkill -f "docker-lb" || true
        sleep 2
    fi
    
    # 启动服务
    nohup ./bin/docker-lb > logs/docker-lb.log 2>&1 &
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if curl -s http://localhost:8080/api/v1/health > /dev/null; then
        log_success "服务启动成功！"
        log_info "Web界面: http://localhost:8080"
        log_info "API文档: http://localhost:8080/api/v1/health"
    else
        log_error "服务启动失败，请检查日志: logs/docker-lb.log"
        exit 1
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "    Docker负载均衡器启动脚本"
    echo "========================================"
    
    check_dependencies
    create_directories
    check_config
    build_backend
    build_frontend
    start_service
    
    echo "========================================"
    log_success "启动完成！"
    echo "========================================"
    
    # 显示状态
    echo ""
    log_info "服务状态:"
    echo "  - 后端服务: http://localhost:8080"
    echo "  - 监控界面: http://localhost:8080"
    echo "  - 日志文件: logs/docker-lb.log"
    echo ""
    log_info "常用命令:"
    echo "  - 查看日志: tail -f logs/docker-lb.log"
    echo "  - 停止服务: pkill -f docker-lb"
    echo "  - 重启服务: ./scripts/restart.sh"
    echo ""
}

# 执行主函数
main "$@"
