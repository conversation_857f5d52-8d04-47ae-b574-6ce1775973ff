@echo off
setlocal enabledelayedexpansion

:: Docker负载均衡器重启脚本 (Windows版本)

echo ========================================
echo     Docker负载均衡器重启脚本
echo ========================================

echo [INFO] 开始重启服务...

:: 停止服务
if exist "scripts\stop.bat" (
    echo [INFO] 停止现有服务...
    call scripts\stop.bat
) else (
    echo [WARNING] stop.bat脚本不存在，尝试手动停止...
    taskkill /f /im docker-lb.exe >nul 2>nul
    timeout /t 3 /nobreak >nul
)

:: 等待一段时间确保服务完全停止
timeout /t 2 /nobreak >nul

:: 启动服务
if exist "scripts\start.bat" (
    echo [INFO] 启动服务...
    call scripts\start.bat
) else (
    echo [ERROR] start.bat脚本不存在
    pause
    exit /b 1
)

echo ========================================
echo [SUCCESS] 重启完成！
echo ========================================
