<template>
  <div id="app">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px" class="sidebar">
        <div class="logo">
          <h2>Docker LB</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Monitor /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          <el-menu-item index="/registries">
            <el-icon><Server /></el-icon>
            <span>镜像源管理</span>
          </el-menu-item>
          <el-menu-item index="/statistics">
            <el-icon><DataAnalysis /></el-icon>
            <span>统计分析</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-content">
            <h1>Docker 负载均衡监控系统</h1>
            <div class="status-indicator">
              <el-badge :value="healthyCount" :max="99" class="item">
                <el-button size="small">健康节点</el-button>
              </el-badge>
              <el-badge :value="totalCount" :max="99" class="item">
                <el-button size="small">总节点</el-button>
              </el-badge>
            </div>
          </div>
        </el-header>

        <!-- 主体内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { Monitor, Server, DataAnalysis } from '@element-plus/icons-vue'
import { useWebSocket } from './composables/useWebSocket'

export default {
  name: 'App',
  components: {
    Monitor,
    Server,
    DataAnalysis
  },
  setup() {
    const healthyCount = ref(0)
    const totalCount = ref(0)
    
    const { connect, disconnect } = useWebSocket()

    onMounted(() => {
      // 连接WebSocket
      connect((data) => {
        if (data.type === 'stats_update' && data.registries) {
          totalCount.value = data.registries.length
          healthyCount.value = data.registries.filter(r => r.healthy).length
        }
      })
    })

    onUnmounted(() => {
      disconnect()
    })

    return {
      healthyCount,
      totalCount
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.sidebar {
  background-color: #304156;
  height: 100vh;
}

.logo {
  padding: 20px;
  text-align: center;
  color: #fff;
  border-bottom: 1px solid #434a50;
}

.logo h2 {
  margin: 0;
  font-size: 18px;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-content h1 {
  font-size: 20px;
  color: #303133;
  margin: 0;
}

.status-indicator {
  display: flex;
  gap: 10px;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}

.el-menu {
  border-right: none;
}
</style>
