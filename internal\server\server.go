package server

import (
	"context"
	"docker-lb/internal/config"
	"docker-lb/internal/loadbalancer"
	"docker-lb/pkg/logger"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// Server HTTP服务器
type Server struct {
	config *config.Config
	lb     *loadbalancer.LoadBalancer
	logger *logger.Logger
	router *gin.Engine
	server *http.Server
	ws     *WebSocketHub
}

// New 创建新的服务器
func New(cfg *config.Config, lb *loadbalancer.LoadBalancer, logger *logger.Logger) *Server {
	// 设置Gin模式
	if cfg.Logging.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// 添加中间件
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(loggingMiddleware(logger))

	s := &Server{
		config: cfg,
		lb:     lb,
		logger: logger,
		router: router,
		ws:     NewWebSocketHub(logger),
	}

	s.setupRoutes()
	
	s.server = &http.Server{
		Addr:    fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	return s
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 静态文件服务
	s.router.Static("/assets", "./web/dist/assets")
	s.router.StaticFile("/", "./web/dist/index.html")

	// API路由组
	api := s.router.Group("/api/v1")
	{
		api.GET("/health", s.getHealth)
		api.GET("/registries", s.getRegistries)
		api.GET("/stats", s.getStats)
		api.POST("/registries/:name/enable", s.enableRegistry)
		api.POST("/registries/:name/disable", s.disableRegistry)
	}

	// WebSocket路由
	s.router.GET("/ws", s.handleWebSocket)

	// Docker代理路由
	s.router.Any("/v2/*path", s.proxyDockerRequest)
}

// Start 启动服务器
func (s *Server) Start() error {
	// 启动WebSocket hub
	go s.ws.Run()

	// 启动监控数据推送
	go s.startMonitoring()

	return s.server.ListenAndServe()
}

// Shutdown 关闭服务器
func (s *Server) Shutdown(ctx context.Context) error {
	s.ws.Close()
	return s.server.Shutdown(ctx)
}

// getHealth 获取健康状态
func (s *Server) getHealth(c *gin.Context) {
	status := s.lb.GetRegistries()
	
	healthyCount := 0
	totalCount := len(status)
	
	for _, reg := range status {
		reg.Mu.RLock()
		if reg.Healthy {
			healthyCount++
		}
		reg.Mu.RUnlock()
	}

	c.JSON(http.StatusOK, gin.H{
		"status":            "ok",
		"timestamp":         time.Now().Format(time.RFC3339),
		"total_registries":  totalCount,
		"healthy_registries": healthyCount,
	})
}

// getRegistries 获取镜像源列表
func (s *Server) getRegistries(c *gin.Context) {
	registries := s.lb.GetRegistries()
	result := make([]map[string]interface{}, 0, len(registries))

	for _, reg := range registries {
		reg.Mu.RLock()
		regInfo := map[string]interface{}{
			"name":              reg.Config.Name,
			"url":               reg.Config.URL,
			"weight":            reg.Config.Weight,
			"enabled":           reg.Config.Enabled,
			"healthy":           reg.Healthy,
			"total_requests":    reg.Stats.TotalRequests,
			"success_requests":  reg.Stats.SuccessRequests,
			"failed_requests":   reg.Stats.FailedRequests,
			"consecutive_fails": reg.Stats.ConsecutiveFails,
			"average_response":  reg.Stats.AverageResponse.Milliseconds(),
			"last_health_check": reg.Stats.LastHealthCheck.Format(time.RFC3339),
		}
		result = append(result, regInfo)
		reg.Mu.RUnlock()
	}

	c.JSON(http.StatusOK, gin.H{
		"registries": result,
	})
}

// getStats 获取统计信息
func (s *Server) getStats(c *gin.Context) {
	registries := s.lb.GetRegistries()
	
	var totalRequests, successRequests, failedRequests int64
	var totalResponseTime time.Duration
	healthyCount := 0

	for _, reg := range registries {
		reg.Mu.RLock()
		totalRequests += reg.Stats.TotalRequests
		successRequests += reg.Stats.SuccessRequests
		failedRequests += reg.Stats.FailedRequests
		totalResponseTime += reg.Stats.AverageResponse
		if reg.Healthy {
			healthyCount++
		}
		reg.Mu.RUnlock()
	}

	var avgResponseTime time.Duration
	if len(registries) > 0 {
		avgResponseTime = totalResponseTime / time.Duration(len(registries))
	}

	var successRate float64
	if totalRequests > 0 {
		successRate = float64(successRequests) / float64(totalRequests) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"total_requests":     totalRequests,
		"success_requests":   successRequests,
		"failed_requests":    failedRequests,
		"success_rate":       successRate,
		"average_response":   avgResponseTime.Milliseconds(),
		"healthy_registries": healthyCount,
		"total_registries":   len(registries),
		"timestamp":          time.Now().Format(time.RFC3339),
	})
}

// enableRegistry 启用镜像源
func (s *Server) enableRegistry(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现启用逻辑
	s.logger.Info("Enable registry: %s", name)
	c.JSON(http.StatusOK, gin.H{"message": "Registry enabled"})
}

// disableRegistry 禁用镜像源
func (s *Server) disableRegistry(c *gin.Context) {
	name := c.Param("name")
	// TODO: 实现禁用逻辑
	s.logger.Info("Disable registry: %s", name)
	c.JSON(http.StatusOK, gin.H{"message": "Registry disabled"})
}

// startMonitoring 启动监控数据推送
func (s *Server) startMonitoring() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if s.ws.HasClients() {
				stats := s.getStatsData()
				s.ws.Broadcast(stats)
			}
		}
	}
}

// getStatsData 获取统计数据
func (s *Server) getStatsData() map[string]interface{} {
	registries := s.lb.GetRegistries()
	result := make([]map[string]interface{}, 0, len(registries))

	for _, reg := range registries {
		reg.Mu.RLock()
		regInfo := map[string]interface{}{
			"name":              reg.Config.Name,
			"healthy":           reg.Healthy,
			"total_requests":    reg.Stats.TotalRequests,
			"success_requests":  reg.Stats.SuccessRequests,
			"failed_requests":   reg.Stats.FailedRequests,
			"average_response":  reg.Stats.AverageResponse.Milliseconds(),
			"last_health_check": reg.Stats.LastHealthCheck.Format(time.RFC3339),
		}
		result = append(result, regInfo)
		reg.Mu.RUnlock()
	}

	return map[string]interface{}{
		"type":       "stats_update",
		"timestamp":  time.Now().Format(time.RFC3339),
		"registries": result,
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggingMiddleware 日志中间件
func loggingMiddleware(logger *logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		c.Next()

		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		logger.Info("%s %s %d %v %s", method, path, statusCode, latency, clientIP)
	}
}
