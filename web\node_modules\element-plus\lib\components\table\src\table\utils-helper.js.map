{"version": 3, "file": "utils-helper.js", "sources": ["../../../../../../../packages/components/table/src/table/utils-helper.ts"], "sourcesContent": ["import type { Store } from '../store'\nimport type { DefaultRow } from './defaults'\n\nfunction useUtils<T extends DefaultRow>(store: Store<T>) {\n  const setCurrentRow = (row: T) => {\n    store.commit('setCurrentRow', row)\n  }\n  const getSelectionRows = () => {\n    return store.getSelectionRows()\n  }\n  const toggleRowSelection = (\n    row: T,\n    selected?: boolean,\n    ignoreSelectable = true\n  ) => {\n    store.toggleRowSelection(row, selected, false, ignoreSelectable)\n    store.updateAllSelected()\n  }\n  const clearSelection = () => {\n    store.clearSelection()\n  }\n  const clearFilter = (columnKeys?: string[] | string) => {\n    store.clearFilter(columnKeys)\n  }\n  const toggleAllSelection = () => {\n    store.commit('toggleAllSelection')\n  }\n  const toggleRowExpansion = (row: T, expanded?: boolean) => {\n    store.toggleRowExpansionAdapter(row, expanded)\n  }\n  const clearSort = () => {\n    store.clearSort()\n  }\n  const sort = (prop: string, order: string) => {\n    store.commit('sort', { prop, order })\n  }\n  const updateKeyChildren = (key: string, data: T[]) => {\n    store.updateKeyChildren(key, data)\n  }\n\n  return {\n    setCurrentRow,\n    getSelectionRows,\n    toggleRowSelection,\n    clearSelection,\n    clearFilter,\n    toggleAllSelection,\n    toggleRowExpansion,\n    clearSort,\n    sort,\n    updateKeyChildren,\n  }\n}\n\nexport default useUtils\n"], "names": [], "mappings": ";;;;AAAA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK;AACjC,IAAI,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,OAAO,KAAK,CAAC,gBAAgB,EAAE,CAAC;AACpC,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,gBAAgB,GAAG,IAAI,KAAK;AACzE,IAAI,KAAK,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;AACrE,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;AAC9B,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,UAAU,KAAK;AACtC,IAAI,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAClC,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,MAAM;AACnC,IAAI,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,kBAAkB,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK;AAChD,IAAI,KAAK,CAAC,yBAAyB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK;AAChC,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AAC1C,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;AAC3C,IAAI,KAAK,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,SAAS;AACb,IAAI,IAAI;AACR,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}