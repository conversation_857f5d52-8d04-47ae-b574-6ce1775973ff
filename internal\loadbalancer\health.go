package loadbalancer

import (
	"context"
	"docker-lb/internal/config"
	"docker-lb/pkg/logger"
	"net/http"
	"net/url"
	"time"
)

// HealthChecker 健康检查器
type HealthChecker struct {
	lb     *LoadBalancer
	config *config.Config
	logger *logger.Logger
	client *http.Client
	stop   chan struct{}
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(lb *LoadBalancer, cfg *config.Config, logger *logger.Logger) *HealthChecker {
	return &HealthChecker{
		lb:     lb,
		config: cfg,
		logger: logger,
		client: &http.Client{
			Timeout: cfg.HealthCheck.Timeout,
		},
		stop: make(chan struct{}),
	}
}

// Start 启动健康检查
func (hc *HealthChecker) Start() {
	hc.logger.Info("Starting health checker with interval: %v", hc.config.HealthCheck.Interval)
	
	ticker := time.NewTicker(hc.config.HealthCheck.Interval)
	defer ticker.Stop()

	// 立即执行一次健康检查
	hc.checkAll()

	for {
		select {
		case <-ticker.C:
			hc.checkAll()
		case <-hc.stop:
			hc.logger.Info("Health checker stopped")
			return
		}
	}
}

// Stop 停止健康检查
func (hc *HealthChecker) Stop() {
	close(hc.stop)
}

// checkAll 检查所有镜像源
func (hc *HealthChecker) checkAll() {
	registries := hc.lb.GetRegistries()
	
	for _, registry := range registries {
		go hc.checkRegistry(registry)
	}
}

// checkRegistry 检查单个镜像源
func (hc *HealthChecker) checkRegistry(registry *Registry) {
	start := time.Now()
	healthy := hc.performHealthCheck(registry)
	responseTime := time.Since(start)

	registry.Mu.Lock()
	registry.Stats.LastHealthCheck = time.Now()

	if healthy {
		if !registry.Healthy {
			hc.logger.Info("Registry %s is now healthy", registry.Config.Name)
		}
		registry.Healthy = true
		registry.Stats.ConsecutiveFails = 0
	} else {
		registry.Stats.ConsecutiveFails++

		// 根据连续失败次数决定是否标记为不健康
		if registry.Stats.ConsecutiveFails >= hc.config.HealthCheck.UnhealthyThreshold {
			if registry.Healthy {
				hc.logger.Warn("Registry %s marked as unhealthy after %d consecutive failures",
					registry.Config.Name, registry.Stats.ConsecutiveFails)
			}
			registry.Healthy = false
		}
	}
	registry.Mu.Unlock()

	// 更新统计信息
	hc.lb.UpdateRegistryStats(registry, healthy, responseTime)
}

// performHealthCheck 执行健康检查
func (hc *HealthChecker) performHealthCheck(registry *Registry) bool {
	// 构建健康检查URL
	baseURL, err := url.Parse(registry.Config.URL)
	if err != nil {
		hc.logger.Error("Invalid registry URL %s: %v", registry.Config.URL, err)
		return false
	}

	healthURL := baseURL.ResolveReference(&url.URL{Path: hc.config.HealthCheck.Endpoint})
	
	// 创建请求
	ctx, cancel := context.WithTimeout(context.Background(), hc.config.HealthCheck.Timeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", healthURL.String(), nil)
	if err != nil {
		hc.logger.Error("Failed to create health check request for %s: %v", registry.Config.Name, err)
		return false
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Docker-LB-HealthChecker/1.0")

	// 执行请求
	resp, err := hc.client.Do(req)
	if err != nil {
		hc.logger.Debug("Health check failed for %s: %v", registry.Config.Name, err)
		return false
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode >= 200 && resp.StatusCode < 400 {
		hc.logger.Debug("Health check passed for %s: %d", registry.Config.Name, resp.StatusCode)
		return true
	}

	hc.logger.Debug("Health check failed for %s: status %d", registry.Config.Name, resp.StatusCode)
	return false
}

// GetHealthStatus 获取健康状态摘要
func (hc *HealthChecker) GetHealthStatus() map[string]interface{} {
	registries := hc.lb.GetRegistries()
	
	status := map[string]interface{}{
		"total_registries":   len(registries),
		"healthy_registries": 0,
		"registries":         make([]map[string]interface{}, 0, len(registries)),
	}

	for _, registry := range registries {
		registry.Mu.RLock()
		regStatus := map[string]interface{}{
			"name":              registry.Config.Name,
			"url":               registry.Config.URL,
			"healthy":           registry.Healthy,
			"weight":            registry.Config.Weight,
			"total_requests":    registry.Stats.TotalRequests,
			"success_requests":  registry.Stats.SuccessRequests,
			"failed_requests":   registry.Stats.FailedRequests,
			"consecutive_fails": registry.Stats.ConsecutiveFails,
			"average_response":  registry.Stats.AverageResponse.Milliseconds(),
			"last_health_check": registry.Stats.LastHealthCheck.Format(time.RFC3339),
		}

		if registry.Healthy {
			status["healthy_registries"] = status["healthy_registries"].(int) + 1
		}

		status["registries"] = append(status["registries"].([]map[string]interface{}), regStatus)
		registry.Mu.RUnlock()
	}

	return status
}
