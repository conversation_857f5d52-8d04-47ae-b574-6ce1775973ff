# 多阶段构建
# 第一阶段：构建Go应用
FROM golang:1.21-alpine AS go-builder

WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY cmd/ cmd/
COPY internal/ internal/
COPY pkg/ pkg/

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o docker-lb cmd/main.go

# 第二阶段：构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# 复制前端文件
COPY web/package*.json ./
RUN npm ci

COPY web/ ./
RUN npm run build

# 第三阶段：最终镜像
FROM alpine:latest

# 安装必要的包
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

WORKDIR /root/

# 从构建阶段复制文件
COPY --from=go-builder /app/docker-lb .
COPY --from=frontend-builder /app/dist ./web/dist/
COPY config.yaml .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/v1/health || exit 1

# 启动应用
CMD ["./docker-lb"]
