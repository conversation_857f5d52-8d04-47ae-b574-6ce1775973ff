import axios from 'axios'

const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

export const apiService = {
  // 获取健康状态
  getHealth() {
    return api.get('/health')
  },

  // 获取镜像源列表
  getRegistries() {
    return api.get('/registries')
  },

  // 获取统计信息
  getStats() {
    return api.get('/stats')
  },

  // 启用镜像源
  enableRegistry(name) {
    return api.post(`/registries/${name}/enable`)
  },

  // 禁用镜像源
  disableRegistry(name) {
    return api.post(`/registries/${name}/disable`)
  }
}

export default api
