# Docker加速负载均衡配置文件
server:
  port: 8080
  host: "0.0.0.0"

# Docker镜像源配置
registries:
  - name: "Docker Hub Official"
    url: "https://registry-1.docker.io"
    weight: 10
    enabled: true
    timeout: 30s
    
  - name: "阿里云镜像源"
    url: "https://registry.cn-hangzhou.aliyuncs.com"
    weight: 8
    enabled: true
    timeout: 30s
    
  - name: "腾讯云镜像源"
    url: "https://mirror.ccs.tencentyun.com"
    weight: 8
    enabled: true
    timeout: 30s
    
  - name: "网易镜像源"
    url: "https://hub-mirror.c.163.com"
    weight: 7
    enabled: true
    timeout: 30s
    
  - name: "中科大镜像源"
    url: "https://docker.mirrors.ustc.edu.cn"
    weight: 6
    enabled: true
    timeout: 30s

# 负载均衡策略
load_balancer:
  strategy: "weighted_round_robin"  # 支持: round_robin, weighted_round_robin, least_connections, hash
  health_check_interval: 30s
  max_retries: 3
  retry_delay: 5s

# 健康检查配置
health_check:
  enabled: true
  endpoint: "/v2/"
  interval: 30s
  timeout: 10s
  unhealthy_threshold: 3
  healthy_threshold: 2

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  file: "logs/docker-lb.log"
  max_size: 100  # MB
  max_backups: 5
  max_age: 30    # days

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  websocket_port: 8081
