package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 主配置结构
type Config struct {
	Server       ServerConfig       `yaml:"server"`
	Registries   []RegistryConfig   `yaml:"registries"`
	LoadBalancer LoadBalancerConfig `yaml:"load_balancer"`
	HealthCheck  HealthCheckConfig  `yaml:"health_check"`
	Logging      LoggingConfig      `yaml:"logging"`
	Monitoring   MonitoringConfig   `yaml:"monitoring"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int    `yaml:"port"`
	Host string `yaml:"host"`
}

// RegistryConfig 镜像源配置
type RegistryConfig struct {
	Name    string        `yaml:"name"`
	URL     string        `yaml:"url"`
	Weight  int           `yaml:"weight"`
	Enabled bool          `yaml:"enabled"`
	Timeout time.Duration `yaml:"timeout"`
}

// LoadBalancerConfig 负载均衡配置
type LoadBalancerConfig struct {
	Strategy            string        `yaml:"strategy"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	MaxRetries          int           `yaml:"max_retries"`
	RetryDelay          time.Duration `yaml:"retry_delay"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled            bool          `yaml:"enabled"`
	Endpoint           string        `yaml:"endpoint"`
	Interval           time.Duration `yaml:"interval"`
	Timeout            time.Duration `yaml:"timeout"`
	UnhealthyThreshold int           `yaml:"unhealthy_threshold"`
	HealthyThreshold   int           `yaml:"healthy_threshold"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `yaml:"level"`
	File       string `yaml:"file"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled       bool `yaml:"enabled"`
	MetricsPort   int  `yaml:"metrics_port"`
	WebsocketPort int  `yaml:"websocket_port"`
}

// Load 加载配置文件
func Load(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 验证配置
	if err := config.validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return &config, nil
}

// validate 验证配置
func (c *Config) validate() error {
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}

	if len(c.Registries) == 0 {
		return fmt.Errorf("no registries configured")
	}

	enabledCount := 0
	for _, reg := range c.Registries {
		if reg.Enabled {
			enabledCount++
			if reg.URL == "" {
				return fmt.Errorf("registry %s has empty URL", reg.Name)
			}
			if reg.Weight <= 0 {
				return fmt.Errorf("registry %s has invalid weight: %d", reg.Name, reg.Weight)
			}
		}
	}

	if enabledCount == 0 {
		return fmt.Errorf("no enabled registries")
	}

	return nil
}
