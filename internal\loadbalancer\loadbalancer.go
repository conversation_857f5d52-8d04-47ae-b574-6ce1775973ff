package loadbalancer

import (
	"docker-lb/internal/config"
	"docker-lb/pkg/logger"
	"fmt"
	"sync"
	"time"
)

// Registry 镜像源信息
type Registry struct {
	Config  config.RegistryConfig
	Healthy bool
	Stats   RegistryStats
	Mu      sync.RWMutex
}

// RegistryStats 镜像源统计信息
type RegistryStats struct {
	TotalRequests    int64         `json:"total_requests"`
	SuccessRequests  int64         `json:"success_requests"`
	FailedRequests   int64         `json:"failed_requests"`
	AverageResponse  time.Duration `json:"average_response"`
	LastHealthCheck  time.Time     `json:"last_health_check"`
	ConsecutiveFails int           `json:"consecutive_fails"`
}

// LoadBalancer 负载均衡器
type LoadBalancer struct {
	config      *config.Config
	registries  []*Registry
	currentIdx  int
	logger      *logger.Logger
	Mu          sync.RWMutex
	healthCheck *HealthChecker
}

// New 创建新的负载均衡器
func New(cfg *config.Config, logger *logger.Logger) *LoadBalancer {
	lb := &LoadBalancer{
		config: cfg,
		logger: logger,
	}

	// 初始化镜像源
	for _, regCfg := range cfg.Registries {
		if regCfg.Enabled {
			registry := &Registry{
				Config:  regCfg,
				Healthy: true, // 初始假设健康
				Stats:   RegistryStats{},
			}
			lb.registries = append(lb.registries, registry)
		}
	}

	// 创建健康检查器
	lb.healthCheck = NewHealthChecker(lb, cfg, logger)

	logger.Info("Load balancer initialized with %d registries", len(lb.registries))
	return lb
}

// GetNextRegistry 获取下一个可用的镜像源
func (lb *LoadBalancer) GetNextRegistry() (*Registry, error) {
	lb.Mu.RLock()
	defer lb.Mu.RUnlock()

	if len(lb.registries) == 0 {
		return nil, fmt.Errorf("no registries available")
	}

	switch lb.config.LoadBalancer.Strategy {
	case "round_robin":
		return lb.roundRobin()
	case "weighted_round_robin":
		return lb.weightedRoundRobin()
	case "least_connections":
		return lb.leastConnections()
	case "hash":
		return lb.hashBased()
	default:
		return lb.roundRobin()
	}
}

// roundRobin 轮询算法
func (lb *LoadBalancer) roundRobin() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	registry := healthyRegistries[lb.currentIdx%len(healthyRegistries)]
	lb.currentIdx++
	return registry, nil
}

// weightedRoundRobin 加权轮询算法
func (lb *LoadBalancer) weightedRoundRobin() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	// 计算总权重
	totalWeight := 0
	for _, reg := range healthyRegistries {
		totalWeight += reg.Config.Weight
	}

	// 根据权重选择
	target := lb.currentIdx % totalWeight
	currentWeight := 0
	
	for _, reg := range healthyRegistries {
		currentWeight += reg.Config.Weight
		if target < currentWeight {
			lb.currentIdx++
			return reg, nil
		}
	}

	// fallback
	lb.currentIdx++
	return healthyRegistries[0], nil
}

// leastConnections 最少连接算法
func (lb *LoadBalancer) leastConnections() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	// 简化实现：选择失败请求最少的
	var bestRegistry *Registry
	minFailed := int64(-1)

	for _, reg := range healthyRegistries {
		reg.Mu.RLock()
		failed := reg.Stats.FailedRequests
		reg.Mu.RUnlock()

		if minFailed == -1 || failed < minFailed {
			minFailed = failed
			bestRegistry = reg
		}
	}

	return bestRegistry, nil
}

// hashBased 基于哈希的算法
func (lb *LoadBalancer) hashBased() (*Registry, error) {
	healthyRegistries := lb.getHealthyRegistries()
	if len(healthyRegistries) == 0 {
		return nil, fmt.Errorf("no healthy registries available")
	}

	// 简化实现：基于时间戳的哈希
	hash := time.Now().UnixNano()
	index := hash % int64(len(healthyRegistries))
	return healthyRegistries[index], nil
}

// getHealthyRegistries 获取健康的镜像源
func (lb *LoadBalancer) getHealthyRegistries() []*Registry {
	var healthy []*Registry
	for _, reg := range lb.registries {
		reg.Mu.RLock()
		if reg.Healthy {
			healthy = append(healthy, reg)
		}
		reg.Mu.RUnlock()
	}
	return healthy
}

// GetRegistries 获取所有镜像源
func (lb *LoadBalancer) GetRegistries() []*Registry {
	lb.Mu.RLock()
	defer lb.Mu.RUnlock()
	
	result := make([]*Registry, len(lb.registries))
	copy(result, lb.registries)
	return result
}

// UpdateRegistryStats 更新镜像源统计
func (lb *LoadBalancer) UpdateRegistryStats(registry *Registry, success bool, responseTime time.Duration) {
	registry.Mu.Lock()
	defer registry.Mu.Unlock()

	registry.Stats.TotalRequests++
	if success {
		registry.Stats.SuccessRequests++
		registry.Stats.ConsecutiveFails = 0
	} else {
		registry.Stats.FailedRequests++
		registry.Stats.ConsecutiveFails++
	}

	// 更新平均响应时间
	if registry.Stats.TotalRequests == 1 {
		registry.Stats.AverageResponse = responseTime
	} else {
		registry.Stats.AverageResponse = (registry.Stats.AverageResponse + responseTime) / 2
	}
}

// StartHealthCheck 启动健康检查
func (lb *LoadBalancer) StartHealthCheck() {
	lb.healthCheck.Start()
}
