# Docker 负载均衡加速器

一个高性能的Docker镜像源负载均衡器，支持多镜像源配置、健康检查、实时监控和Web管理界面。

## 功能特性

- 🚀 **多镜像源支持**: 支持配置多个Docker镜像源，自动负载均衡
- ⚖️ **智能负载均衡**: 支持轮询、加权轮询、最少连接等多种负载均衡策略
- 🔍 **健康检查**: 实时监控镜像源健康状态，自动剔除异常节点
- 📊 **实时监控**: Web界面实时展示镜像源状态和统计信息
- 🛠️ **配置管理**: 支持YAML配置文件，灵活配置各种参数
- 📈 **统计分析**: 详细的请求统计和性能分析
- 🔄 **自动重试**: 请求失败时自动重试其他镜像源
- 🌐 **WebSocket**: 实时推送状态更新到前端界面

## 系统架构

```
[客户端] → [负载均衡器] → [镜像源A/B/C/D] → [Docker官方服务器]
                ↓
           [健康检查] → [哈希验证] → [统计监控]
```

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd DockerLB
```

### 2. 配置镜像源

编辑 `config.yaml` 文件，配置您的镜像源：

```yaml
registries:
  - name: "阿里云镜像源"
    url: "https://registry.cn-hangzhou.aliyuncs.com"
    weight: 8
    enabled: true
    timeout: 30s
  # 添加更多镜像源...
```

### 3. 启动后端服务

```bash
# 安装Go依赖
go mod tidy

# 启动服务
go run cmd/main.go
```

### 4. 启动前端界面

```bash
# 进入前端目录
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问监控界面

打开浏览器访问: http://localhost:3000

## 配置说明

### 服务器配置

```yaml
server:
  port: 8080        # 服务端口
  host: "0.0.0.0"   # 监听地址
```

### 镜像源配置

```yaml
registries:
  - name: "镜像源名称"
    url: "https://registry.example.com"
    weight: 10        # 权重 (1-100)
    enabled: true     # 是否启用
    timeout: 30s      # 超时时间
```

### 负载均衡策略

- `round_robin`: 轮询
- `weighted_round_robin`: 加权轮询 (默认)
- `least_connections`: 最少连接
- `hash`: 基于哈希

### 健康检查配置

```yaml
health_check:
  enabled: true
  endpoint: "/v2/"           # 健康检查端点
  interval: 30s              # 检查间隔
  timeout: 10s               # 超时时间
  unhealthy_threshold: 3     # 不健康阈值
  healthy_threshold: 2       # 健康阈值
```

## Docker使用

### 配置Docker客户端

将负载均衡器设置为Docker镜像源：

```bash
# 方法1: 修改daemon.json
sudo vim /etc/docker/daemon.json
```

```json
{
  "registry-mirrors": ["http://localhost:8080"]
}
```

```bash
# 重启Docker服务
sudo systemctl restart docker
```

### 测试拉取镜像

```bash
# 测试拉取镜像
docker pull nginx:latest

# 查看负载均衡器日志
tail -f logs/docker-lb.log
```

## API接口

### 获取健康状态

```bash
GET /api/v1/health
```

### 获取镜像源列表

```bash
GET /api/v1/registries
```

### 获取统计信息

```bash
GET /api/v1/stats
```

### 启用/禁用镜像源

```bash
POST /api/v1/registries/{name}/enable
POST /api/v1/registries/{name}/disable
```

## 监控界面

### 仪表板
- 实时显示健康节点数量
- 总请求数和成功率
- 镜像源状态概览
- 健康状态分布图

### 镜像源管理
- 镜像源列表和状态
- 请求统计信息
- 响应时间监控
- 启用/禁用操作

### 统计分析
- 详细的请求统计
- 响应时间分析
- 成功率趋势
- 性能对比图表

## 部署建议

### 生产环境部署

1. **使用反向代理**: 建议在前面部署Nginx等反向代理
2. **SSL证书**: 配置HTTPS证书保证安全
3. **监控告警**: 集成监控系统，设置告警规则
4. **日志管理**: 配置日志轮转和集中收集
5. **高可用**: 部署多个实例实现高可用

### Docker部署

```dockerfile
# Dockerfile示例
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o docker-lb cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/docker-lb .
COPY --from=builder /app/config.yaml .
CMD ["./docker-lb"]
```

## 故障排除

### 常见问题

1. **镜像源连接失败**
   - 检查网络连接
   - 验证镜像源URL
   - 查看防火墙设置

2. **健康检查失败**
   - 检查健康检查端点
   - 调整超时时间
   - 查看镜像源状态

3. **前端无法连接**
   - 检查后端服务状态
   - 验证端口配置
   - 查看CORS设置

### 日志查看

```bash
# 查看应用日志
tail -f logs/docker-lb.log

# 查看错误日志
grep ERROR logs/docker-lb.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系维护者。
