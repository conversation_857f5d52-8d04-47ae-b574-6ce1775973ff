<template>
  <div class="registry-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>镜像源管理</span>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table :data="registries" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="名称" width="200">
          <template #default="scope">
            <div class="registry-name">
              <el-icon :color="scope.row.healthy ? '#67C23A' : '#F56C6C'">
                <CircleCheck v-if="scope.row.healthy" />
                <CircleClose v-else />
              </el-icon>
              <span style="margin-left: 8px">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="url" label="URL" min-width="300">
          <template #default="scope">
            <el-link :href="scope.row.url" target="_blank" type="primary">
              {{ scope.row.url }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="weight" label="权重" width="80" align="center">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.weight }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="healthy" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.healthy ? 'success' : 'danger'" size="small">
              {{ scope.row.healthy ? '健康' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="请求统计" width="150" align="center">
          <template #default="scope">
            <div class="request-stats">
              <div class="stat-item">
                <span class="stat-label">总数:</span>
                <span class="stat-value">{{ scope.row.total_requests }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功:</span>
                <span class="stat-value success">{{ scope.row.success_requests }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">失败:</span>
                <span class="stat-value error">{{ scope.row.failed_requests }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="响应时间" width="120" align="center">
          <template #default="scope">
            <div class="response-time">
              <span class="time-value">{{ scope.row.average_response }}ms</span>
              <el-progress 
                :percentage="getResponseTimePercentage(scope.row.average_response)"
                :color="getResponseTimeColor(scope.row.average_response)"
                :show-text="false"
                :stroke-width="4"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="最后检查" width="160" align="center">
          <template #default="scope">
            <div class="last-check">
              <div class="check-time">{{ formatTime(scope.row.last_health_check) }}</div>
              <div class="consecutive-fails" v-if="scope.row.consecutive_fails > 0">
                <el-tag type="warning" size="small">
                  连续失败: {{ scope.row.consecutive_fails }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.enabled"
              type="warning"
              size="small"
              @click="disableRegistry(scope.row.name)"
            >
              禁用
            </el-button>
            <el-button
              v-else
              type="success"
              size="small"
              @click="enableRegistry(scope.row.name)"
            >
              启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheck, CircleClose, Refresh } from '@element-plus/icons-vue'
import { apiService } from '../services/api'
import { useWebSocket } from '../composables/useWebSocket'

export default {
  name: 'RegistryList',
  components: {
    CircleCheck,
    CircleClose,
    Refresh
  },
  setup() {
    const registries = ref([])
    const loading = ref(false)

    const { connect, disconnect } = useWebSocket()

    const loadData = async () => {
      loading.value = true
      try {
        const data = await apiService.getRegistries()
        registries.value = data.registries || []
      } catch (error) {
        console.error('Failed to load registries:', error)
        ElMessage.error('加载镜像源列表失败')
      } finally {
        loading.value = false
      }
    }

    const refreshData = () => {
      loadData()
    }

    const enableRegistry = async (name) => {
      try {
        await apiService.enableRegistry(name)
        ElMessage.success(`镜像源 ${name} 已启用`)
        loadData()
      } catch (error) {
        console.error('Failed to enable registry:', error)
        ElMessage.error(`启用镜像源 ${name} 失败`)
      }
    }

    const disableRegistry = async (name) => {
      try {
        await apiService.disableRegistry(name)
        ElMessage.success(`镜像源 ${name} 已禁用`)
        loadData()
      } catch (error) {
        console.error('Failed to disable registry:', error)
        ElMessage.error(`禁用镜像源 ${name} 失败`)
      }
    }

    const formatTime = (timeStr) => {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }

    const getResponseTimePercentage = (time) => {
      // 将响应时间转换为百分比，最大值设为2000ms
      const maxTime = 2000
      return Math.min((time / maxTime) * 100, 100)
    }

    const getResponseTimeColor = (time) => {
      if (time < 500) return '#67C23A'
      if (time < 1000) return '#E6A23C'
      return '#F56C6C'
    }

    onMounted(() => {
      loadData()
      
      // 连接WebSocket接收实时更新
      connect((data) => {
        if (data.type === 'stats_update' && data.registries) {
          registries.value = data.registries
        }
      })
    })

    onUnmounted(() => {
      disconnect()
    })

    return {
      registries,
      loading,
      refreshData,
      enableRegistry,
      disableRegistry,
      formatTime,
      getResponseTimePercentage,
      getResponseTimeColor
    }
  }
}
</script>

<style scoped>
.registry-list {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.registry-name {
  display: flex;
  align-items: center;
}

.request-stats {
  font-size: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.stat-label {
  color: #909399;
}

.stat-value.success {
  color: #67C23A;
}

.stat-value.error {
  color: #F56C6C;
}

.response-time {
  text-align: center;
}

.time-value {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
}

.last-check {
  font-size: 12px;
}

.check-time {
  margin-bottom: 5px;
  color: #606266;
}

.consecutive-fails {
  margin-top: 5px;
}
</style>
