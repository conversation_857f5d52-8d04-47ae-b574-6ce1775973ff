{"name": "docker-lb-frontend", "version": "1.0.0", "description": "Docker <PERSON>ad Ba<PERSON>r <PERSON>", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "axios": "^1.4.0", "chart.js": "^4.3.0", "vue-chartjs": "^5.2.0", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.0"}}