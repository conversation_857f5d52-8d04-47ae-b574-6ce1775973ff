package server

import (
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// proxyDockerRequest 代理Docker请求
func (s *Server) proxyDockerRequest(c *gin.Context) {
	start := time.Now()
	
	// 获取下一个可用的镜像源
	registry, err := s.lb.GetNextRegistry()
	if err != nil {
		s.logger.Error("No available registry: %v", err)
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "No available registry",
		})
		return
	}

	// 构建目标URL
	targetURL, err := s.buildTargetURL(registry.Config.URL, c.Request.URL.Path, c.Request.URL.RawQuery)
	if err != nil {
		s.logger.Error("Failed to build target URL: %v", err)
		c.<PERSON>SO<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "Failed to build target URL",
		})
		return
	}

	// 创建代理请求
	proxyReq, err := http.NewRequest(c.Request.Method, targetURL, c.Request.Body)
	if err != nil {
		s.logger.Error("Failed to create proxy request: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create proxy request",
		})
		return
	}

	// 复制请求头
	s.copyHeaders(c.Request.Header, proxyReq.Header)
	
	// 设置超时
	client := &http.Client{
		Timeout: registry.Config.Timeout,
	}

	// 执行请求
	resp, err := client.Do(proxyReq)
	responseTime := time.Since(start)
	
	if err != nil {
		s.logger.Error("Proxy request failed for %s: %v", registry.Config.Name, err)
		s.lb.UpdateRegistryStats(registry, false, responseTime)
		
		// 尝试重试其他镜像源
		if s.config.LoadBalancer.MaxRetries > 0 {
			s.retryRequest(c, 1)
			return
		}
		
		c.JSON(http.StatusBadGateway, gin.H{
			"error": "Proxy request failed",
		})
		return
	}
	defer resp.Body.Close()

	// 更新统计信息
	success := resp.StatusCode < 400
	s.lb.UpdateRegistryStats(registry, success, responseTime)

	// 复制响应头
	s.copyHeaders(resp.Header, c.Writer.Header())
	c.Status(resp.StatusCode)

	// 复制响应体
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		s.logger.Error("Failed to copy response body: %v", err)
	}

	s.logger.Debug("Proxied request to %s: %s %s -> %d (%v)", 
		registry.Config.Name, c.Request.Method, c.Request.URL.Path, resp.StatusCode, responseTime)
}

// retryRequest 重试请求
func (s *Server) retryRequest(c *gin.Context, attempt int) {
	if attempt > s.config.LoadBalancer.MaxRetries {
		c.JSON(http.StatusBadGateway, gin.H{
			"error": "All registries failed",
		})
		return
	}

	// 等待重试延迟
	time.Sleep(s.config.LoadBalancer.RetryDelay)

	// 获取下一个镜像源
	registry, err := s.lb.GetNextRegistry()
	if err != nil {
		s.logger.Error("No available registry for retry %d: %v", attempt, err)
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "No available registry",
		})
		return
	}

	s.logger.Info("Retrying request with %s (attempt %d)", registry.Config.Name, attempt)
	
	// 重新执行代理请求逻辑
	// 这里简化处理，实际应该重构代理逻辑
	s.proxyDockerRequest(c)
}

// buildTargetURL 构建目标URL
func (s *Server) buildTargetURL(baseURL, path, query string) (string, error) {
	base, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	target := base.ResolveReference(&url.URL{
		Path:     path,
		RawQuery: query,
	})

	return target.String(), nil
}

// copyHeaders 复制HTTP头
func (s *Server) copyHeaders(src, dst http.Header) {
	for key, values := range src {
		// 跳过一些不应该转发的头
		if s.shouldSkipHeader(key) {
			continue
		}
		
		for _, value := range values {
			dst.Add(key, value)
		}
	}
}

// shouldSkipHeader 判断是否应该跳过某个头
func (s *Server) shouldSkipHeader(header string) bool {
	skipHeaders := []string{
		"Connection",
		"Keep-Alive",
		"Proxy-Authenticate",
		"Proxy-Authorization",
		"Te",
		"Trailers",
		"Transfer-Encoding",
		"Upgrade",
	}

	header = strings.ToLower(header)
	for _, skip := range skipHeaders {
		if strings.ToLower(skip) == header {
			return true
		}
	}
	return false
}
