2025/07/27 22:01:30 [2025-07-27 22:01:30] INFO: Load balancer initialized with 5 registries
2025/07/27 22:01:30 [2025-07-27 22:01:30] INFO: Starting health checker with interval: 30s
2025/07/27 22:01:30 [2025-07-27 22:01:30] INFO: Starting Docker Load Balancer on 0.0.0.0:8080
2025/07/27 22:01:51 [2025-07-27 22:01:51] INFO: GET /api/v1/health 200 0s ::1
2025/07/27 22:02:00 [2025-07-27 22:02:00] WARN: Registry 腾讯云镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:02:00 [2025-07-27 22:02:00] WARN: Registry 中科大镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:02:00 [2025-07-27 22:02:00] WARN: Registry 网易镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:02:00 [2025-07-27 22:02:00] WARN: Registry 阿里云镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:02:10 [2025-07-27 22:02:10] WARN: Registry Docker Hub Official marked as unhealthy after 3 consecutive failures
2025/07/27 22:02:11 [2025-07-27 22:02:11] INFO: GET / 200 37.4148ms ::1
2025/07/27 22:02:11 [2025-07-27 22:02:11] INFO: GET /assets/index-e11c92f2.js 404 0s ::1
2025/07/27 22:02:11 [2025-07-27 22:02:11] INFO: GET /assets/index-208d8145.css 404 0s ::1
2025/07/27 22:02:11 [2025-07-27 22:02:11] INFO: GET /vite.svg 404 0s ::1
2025/07/27 22:02:14 [2025-07-27 22:02:14] INFO: GET / 304 0s ::1
2025/07/27 22:02:14 [2025-07-27 22:02:14] INFO: GET /assets/index-e11c92f2.js 404 0s ::1
2025/07/27 22:02:14 [2025-07-27 22:02:14] INFO: GET /assets/index-208d8145.css 404 0s ::1
2025/07/27 22:02:21 [2025-07-27 22:02:21] INFO: GET /api/v1/registries 200 245.5µs ::1
2025/07/27 22:02:42 [2025-07-27 22:02:42] INFO: GET / 304 0s ::1
2025/07/27 22:02:43 [2025-07-27 22:02:43] INFO: GET /assets/index-e11c92f2.js 404 0s ::1
2025/07/27 22:02:43 [2025-07-27 22:02:43] INFO: GET /assets/index-208d8145.css 404 0s ::1
2025/07/27 22:06:17 [2025-07-27 22:06:17] INFO: Load balancer initialized with 5 registries
2025/07/27 22:06:17 [2025-07-27 22:06:17] INFO: Starting health checker with interval: 30s
2025/07/27 22:06:17 [2025-07-27 22:06:17] INFO: Starting Docker Load Balancer on 0.0.0.0:8080
2025/07/27 22:06:22 [2025-07-27 22:06:22] INFO: GET / 304 554.8µs ::1
2025/07/27 22:06:22 [2025-07-27 22:06:22] INFO: GET /assets/index-208d8145.css 200 35.8535ms ::1
2025/07/27 22:06:22 [2025-07-27 22:06:22] INFO: GET /assets/index-e11c92f2.js 200 36.8507ms ::1
2025/07/27 22:06:23 [2025-07-27 22:06:23] INFO: WebSocket client connected, total: 1
2025/07/27 22:06:23 [2025-07-27 22:06:23] INFO: GET /ws 200 554.6µs ::1
2025/07/27 22:06:23 [2025-07-27 22:06:23] INFO: WebSocket client connected, total: 2
2025/07/27 22:06:23 [2025-07-27 22:06:23] INFO: GET /ws 200 0s ::1
2025/07/27 22:06:23 [2025-07-27 22:06:23] INFO: GET /api/v1/registries 200 607.3µs ::1
2025/07/27 22:06:23 [2025-07-27 22:06:23] INFO: GET /api/v1/stats 200 510.6µs ::1
2025/07/27 22:06:47 [2025-07-27 22:06:47] WARN: Registry 中科大镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:06:47 [2025-07-27 22:06:47] WARN: Registry 腾讯云镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:06:47 [2025-07-27 22:06:47] WARN: Registry 网易镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:06:47 [2025-07-27 22:06:47] WARN: Registry 阿里云镜像源 marked as unhealthy after 3 consecutive failures
2025/07/27 22:06:51 [2025-07-27 22:06:51] ERROR: WebSocket error: websocket: close 1005 (no status)
2025/07/27 22:06:51 [2025-07-27 22:06:51] INFO: WebSocket client disconnected, total: 1
2025/07/27 22:06:51 [2025-07-27 22:06:51] INFO: GET /ws 200 0s ::1
2025/07/27 22:06:51 [2025-07-27 22:06:51] INFO: WebSocket client connected, total: 2
2025/07/27 22:06:51 [2025-07-27 22:06:51] INFO: GET /api/v1/registries 200 0s ::1
2025/07/27 22:06:56 [2025-07-27 22:06:56] INFO: Enable registry: Docker Hub Official
2025/07/27 22:06:56 [2025-07-27 22:06:56] INFO: POST /api/v1/registries/Docker Hub Official/enable 200 0s ::1
2025/07/27 22:06:56 [2025-07-27 22:06:56] INFO: GET /api/v1/registries 200 0s ::1
2025/07/27 22:06:57 [2025-07-27 22:06:57] WARN: Registry Docker Hub Official marked as unhealthy after 3 consecutive failures
2025/07/27 22:07:03 [2025-07-27 22:07:03] ERROR: WebSocket error: websocket: close 1005 (no status)
2025/07/27 22:07:03 [2025-07-27 22:07:03] INFO: WebSocket client disconnected, total: 1
2025/07/27 22:07:03 [2025-07-27 22:07:03] INFO: GET /ws 200 0s ::1
2025/07/27 22:07:03 [2025-07-27 22:07:03] INFO: WebSocket client connected, total: 2
2025/07/27 22:07:03 [2025-07-27 22:07:03] INFO: GET /api/v1/registries 200 178.4µs ::1
2025/07/27 22:07:03 [2025-07-27 22:07:03] INFO: GET /api/v1/stats 200 0s ::1
2025/07/27 22:07:09 [2025-07-27 22:07:09] ERROR: WebSocket error: websocket: close 1005 (no status)
2025/07/27 22:07:09 [2025-07-27 22:07:09] INFO: WebSocket client disconnected, total: 1
2025/07/27 22:07:09 [2025-07-27 22:07:09] INFO: GET /ws 200 0s ::1
2025/07/27 22:07:09 [2025-07-27 22:07:09] INFO: WebSocket client connected, total: 2
2025/07/27 22:07:10 [2025-07-27 22:07:10] INFO: GET /api/v1/registries 200 0s ::1
2025/07/27 22:07:10 [2025-07-27 22:07:10] INFO: GET /api/v1/stats 200 0s ::1
2025/07/27 22:07:16 [2025-07-27 22:07:16] INFO: GET /api/v1/health 200 0s ::1
2025/07/27 22:12:31 [2025-07-27 22:12:31] INFO: GET / 200 0s ::1
2025/07/27 22:13:03 [2025-07-27 22:13:03] INFO: GET /assets/index-208d8145.css 200 548.7µs ::1
