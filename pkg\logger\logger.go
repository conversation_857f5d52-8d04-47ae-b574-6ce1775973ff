package logger

import (
	"docker-lb/internal/config"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Level 日志级别
type Level int

const (
	DEBUG Level = iota
	INFO
	WARN
	ERROR
)

// Logger 日志器
type Logger struct {
	level  Level
	logger *log.Logger
	file   *os.File
}

// New 创建新的日志器
func New(cfg config.LoggingConfig) *Logger {
	level := parseLevel(cfg.Level)
	
	var file *os.File
	var logger *log.Logger

	if cfg.File != "" {
		// 确保日志目录存在
		dir := filepath.Dir(cfg.File)
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Printf("Failed to create log directory: %v", err)
			logger = log.New(os.Stdout, "", log.LstdFlags)
		} else {
			var err error
			file, err = os.OpenFile(cfg.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				log.Printf("Failed to open log file: %v", err)
				logger = log.New(os.Stdout, "", log.LstdFlags)
			} else {
				logger = log.New(file, "", log.LstdFlags)
			}
		}
	} else {
		logger = log.New(os.Stdout, "", log.LstdFlags)
	}

	return &Logger{
		level:  level,
		logger: logger,
		file:   file,
	}
}

// parseLevel 解析日志级别
func parseLevel(levelStr string) Level {
	switch strings.ToLower(levelStr) {
	case "debug":
		return DEBUG
	case "info":
		return INFO
	case "warn", "warning":
		return WARN
	case "error":
		return ERROR
	default:
		return INFO
	}
}

// Debug 调试日志
func (l *Logger) Debug(format string, args ...interface{}) {
	if l.level <= DEBUG {
		l.log("DEBUG", format, args...)
	}
}

// Info 信息日志
func (l *Logger) Info(format string, args ...interface{}) {
	if l.level <= INFO {
		l.log("INFO", format, args...)
	}
}

// Warn 警告日志
func (l *Logger) Warn(format string, args ...interface{}) {
	if l.level <= WARN {
		l.log("WARN", format, args...)
	}
}

// Error 错误日志
func (l *Logger) Error(format string, args ...interface{}) {
	if l.level <= ERROR {
		l.log("ERROR", format, args...)
	}
}

// log 内部日志方法
func (l *Logger) log(level, format string, args ...interface{}) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[%s] %s: %s", timestamp, level, message)
}

// Close 关闭日志器
func (l *Logger) Close() error {
	if l.file != nil {
		return l.file.Close()
	}
	return nil
}
